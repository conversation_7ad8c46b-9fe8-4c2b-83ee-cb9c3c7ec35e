"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js":
/*!********************************************************!*\
  !*** ./src/components/settings/CompanyDetailsModal.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Modal */ \"(app-pages-browser)/./src/components/ui/Modal.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Award,BarChart3,Building,Calendar,CheckCircle,Clock,CreditCard,Database,DollarSign,Download,ExternalLink,FileText,Globe,Info,Mail,MapPin,Package,Phone,Settings,Shield,Star,Target,TrendingUp,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyLogoService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyLogoService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CompanyDetailsModal = (param)=>{\n    let { isOpen, onClose, companyId } = param;\n    _s();\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompanyDetailsModal.useEffect\": ()=>{\n            if (isOpen && companyId) {\n                loadCompanyDetails();\n            }\n        }\n    }[\"CompanyDetailsModal.useEffect\"], [\n        isOpen,\n        companyId\n    ]);\n    // Função para processar campos JSON que podem vir como string\n    const parseJsonField = function(jsonData) {\n        let fieldName = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'campo';\n        if (!jsonData) return {};\n        // Se já for um objeto, retornar como está\n        if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {\n            return jsonData;\n        }\n        // Se for uma string, tentar fazer o parse\n        if (typeof jsonData === 'string') {\n            try {\n                // Verificar se a string está vazia ou não é um JSON válido\n                if (!jsonData.trim() || !jsonData.startsWith('{') && !jsonData.startsWith('[')) {\n                    console.warn(\"[CompanyDetailsModal] \".concat(fieldName, \" n\\xe3o parece ser um JSON v\\xe1lido:\"), jsonData);\n                    return {};\n                }\n                return JSON.parse(jsonData);\n            } catch (error) {\n                console.error(\"[CompanyDetailsModal] Erro ao fazer parse do \".concat(fieldName, \":\"), error);\n                return {};\n            }\n        }\n        return {};\n    };\n    // Alias para manter compatibilidade\n    const parseSocialMedia = (data)=>parseJsonField(data, 'socialMedia');\n    const parseBusinessHours = (data)=>parseJsonField(data, 'businessHours');\n    const loadCompanyDetails = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log('[CompanyDetailsModal] Buscando empresa com ID:', companyId);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.api.get(\"/companies/\".concat(companyId));\n            console.log('[CompanyDetailsModal] Resposta da API:', response.data);\n            // Verificar se a empresa tem documentos/logo\n            if (response.data.documents && response.data.documents.length > 0 && response.data.documents[0] && response.data.documents[0].path) {\n                console.log('[CompanyDetailsModal] Documentos encontrados:', response.data.documents);\n                console.log('[CompanyDetailsModal] Caminho do logo:', response.data.documents[0].path);\n                // Tentar construir a URL do logo\n                const logoUrl = _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__.companyLogoService.getCompanyLogoUrl(response.data.id, response.data.documents[0].path);\n                console.log('[CompanyDetailsModal] URL do logo construída:', logoUrl);\n            } else {\n                console.log('[CompanyDetailsModal] Empresa não tem documentos/logo ou caminho inválido');\n            }\n            setCompany(response.data);\n        } catch (error) {\n            console.error('[CompanyDetailsModal] Erro ao carregar detalhes da empresa:', error);\n            setError('Não foi possível carregar os detalhes da empresa. Tente novamente mais tarde.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: \"Detalhes da Empresa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-red-500 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, undefined) : company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex flex-col items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-32 w-32 rounded-lg bg-neutral-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden mb-4\",\n                                            children: company.documents && company.documents[0] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: company.documents && company.documents[0] && company.documents[0].path ? _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__.companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : '',\n                                                    alt: company.name,\n                                                    className: \"h-full w-full object-contain\",\n                                                    onLoad: ()=>console.log('[CompanyDetailsModal] Imagem carregada com sucesso'),\n                                                    onError: (e)=>{\n                                                        console.error('[CompanyDetailsModal] Erro ao carregar imagem:', e.target.src);\n                                                        e.target.onerror = null;\n                                                        e.target.style.display = 'none';\n                                                        e.target.parentNode.innerHTML = '<div class=\"h-16 w-16 text-neutral-400 dark:text-gray-500\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"20\" height=\"14\" x=\"2\" y=\"7\" rx=\"2\" ry=\"2\"/><path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"/></svg></div>';\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-16 w-16 text-neutral-400 dark:text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                lineNumber: 145,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-neutral-800 dark:text-white text-center\",\n                                            children: company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        company.tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-neutral-500 dark:text-gray-400 text-center\",\n                                            children: company.tradingName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 text-xs rounded-full\",\n                                            children: company.active ? \"Ativa\" : \"Inativa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"CNPJ:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.cnpj ? company.cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Telefone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.phone || \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        company.phone2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Telefone 2:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.phone2\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        company.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Website:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: company.website.startsWith('http') ? company.website : \"https://\".concat(company.website),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"text-primary-600 dark:text-primary-400 font-medium hover:underline\",\n                                                                    children: company.website\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Endere\\xe7o\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Endere\\xe7o:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.address || \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Cidade/Estado:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.city && company.state ? \"\".concat(company.city, \"/\").concat(company.state) : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"CEP:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.postalCode || \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, undefined),\n                        company.subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-neutral-50 dark:bg-gray-800 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-3 flex items-center gap-1.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Informa\\xe7\\xf5es de Assinatura\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-neutral-700 dark:text-gray-300\",\n                                                            children: \"Plano\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-neutral-800 dark:text-gray-200 font-medium\",\n                                                    children: company.subscriptionInfo.planName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-gray-400\",\n                                                    children: [\n                                                        company.subscriptionInfo.moduleCount,\n                                                        \" m\\xf3dulos ativos\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-neutral-700 dark:text-gray-300\",\n                                                            children: \"Pagamento\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        company.subscriptionInfo.paymentStatus === 'up_to_date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-green-600 dark:text-green-400\",\n                                                                    children: \"Em dia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true),\n                                                        company.subscriptionInfo.paymentStatus === 'overdue' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-red-600 dark:text-red-400\",\n                                                                    children: \"Atrasado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true),\n                                                        company.subscriptionInfo.paymentStatus === 'expired' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-orange-600 dark:text-orange-400\",\n                                                                    children: \"Vencido\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true),\n                                                        company.subscriptionInfo.paymentStatus === 'no_subscription' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                    children: \"Sem plano\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                company.subscriptionInfo.pricePerMonth > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-gray-400\",\n                                                    children: [\n                                                        \"R$ \",\n                                                        parseFloat(company.subscriptionInfo.pricePerMonth).toFixed(2),\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 text-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-neutral-700 dark:text-gray-300\",\n                                                            children: \"Usu\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-neutral-800 dark:text-gray-200 font-medium\",\n                                                    children: [\n                                                        company.subscriptionInfo.userCount,\n                                                        company.subscriptionInfo.userLimit > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-neutral-500 dark:text-gray-400\",\n                                                            children: [\n                                                                \"/\",\n                                                                company.subscriptionInfo.userLimit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                company.subscriptionInfo.userUsagePercentage > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-neutral-200 dark:bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2 rounded-full \".concat(company.subscriptionInfo.userUsagePercentage >= 90 ? 'bg-red-500' : company.subscriptionInfo.userUsagePercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'),\n                                                        style: {\n                                                            width: \"\".concat(Math.min(company.subscriptionInfo.userUsagePercentage, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, undefined),\n                                company.subscriptionInfo.nextDueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-3 border-t border-neutral-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300\",\n                                                    children: \"Pr\\xf3ximo Vencimento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 324,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-neutral-800 dark:text-gray-200\",\n                                                    children: new Date(company.subscriptionInfo.nextDueDate).toLocaleDateString('pt-BR')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                company.subscriptionInfo.daysUntilExpiry !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(company.subscriptionInfo.daysUntilExpiry <= 0 ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : company.subscriptionInfo.daysUntilExpiry <= 7 ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400' : 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'),\n                                                    children: company.subscriptionInfo.daysUntilExpiry <= 0 ? 'Vencido' : \"\".concat(company.subscriptionInfo.daysUntilExpiry, \" dias\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 328,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 323,\n                                    columnNumber: 19\n                                }, undefined),\n                                company.subscriptionInfo.modules && company.subscriptionInfo.modules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-3 border-t border-neutral-200 dark:border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300\",\n                                                    children: \"M\\xf3dulos Contratados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 353,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: company.subscriptionInfo.modules.map((module, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full\",\n                                                    children: module.type || module\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 357,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 352,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 230,\n                            columnNumber: 15\n                        }, undefined),\n                        company.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                    children: \"Descri\\xe7\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 374,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-600 dark:text-gray-300 bg-neutral-50 dark:bg-gray-700 p-3 rounded-md\",\n                                    children: company.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 375,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 373,\n                            columnNumber: 15\n                        }, undefined),\n                        company.socialMedia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                    children: \"Redes Sociais\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 383,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                                    children: Object.entries(parseSocialMedia(company.socialMedia) || {}).map((param)=>{\n                                        let [key, value] = param;\n                                        return value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: value.startsWith('http') ? value : \"https://\".concat(value),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Award_BarChart3_Building_Calendar_CheckCircle_Clock_CreditCard_Database_DollarSign_Download_ExternalLink_FileText_Globe_Info_Mail_MapPin_Package_Phone_Settings_Shield_Star_Target_TrendingUp_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (()=>{\n                                                    // Mapear nomes de redes sociais para exibição\n                                                    const socialNames = {\n                                                        facebook: 'Facebook',\n                                                        instagram: 'Instagram',\n                                                        linkedin: 'LinkedIn',\n                                                        twitter: 'Twitter',\n                                                        youtube: 'YouTube',\n                                                        website: 'Website',\n                                                        email: 'Email'\n                                                    };\n                                                    return socialNames[key] || key.charAt(0).toUpperCase() + key.slice(1);\n                                                })()\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 387,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 382,\n                            columnNumber: 15\n                        }, undefined),\n                        company.businessHours && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                    children: \"Hor\\xe1rio de Funcionamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 417,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                                    children: Object.entries(parseBusinessHours(company.businessHours) || {}).map((param)=>{\n                                        let [day, hours] = param;\n                                        return hours && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                    children: [\n                                                        day === 'monday' && 'Segunda-feira',\n                                                        day === 'tuesday' && 'Terça-feira',\n                                                        day === 'wednesday' && 'Quarta-feira',\n                                                        day === 'thursday' && 'Quinta-feira',\n                                                        day === 'friday' && 'Sexta-feira',\n                                                        day === 'saturday' && 'Sábado',\n                                                        day === 'sunday' && 'Domingo'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                    children: typeof hours === 'string' ? hours : hours.start && hours.end ? \"\".concat(hours.start, \" \\xe0s \").concat(hours.end) : JSON.stringify(hours)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, day, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 421,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 418,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 416,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-neutral-500 dark:text-neutral-400\",\n                    children: \"Nenhuma informa\\xe7\\xe3o dispon\\xedvel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end p-4 border-t border-neutral-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors\",\n                    children: \"Fechar\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 452,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompanyDetailsModal, \"4yWIl/UcXWN+pzF47f2vnWUbQ9c=\");\n_c = CompanyDetailsModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyDetailsModal);\nvar _c;\n$RefreshReg$(_c, \"CompanyDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js\n"));

/***/ })

});