"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/settings/page",{

/***/ "(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js":
/*!********************************************************!*\
  !*** ./src/components/settings/CompanyDetailsModal.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Modal */ \"(app-pages-browser)/./src/components/ui/Modal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Globe,Mail,MapPin,Package,Phone,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Globe,Mail,MapPin,Package,Phone,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Globe,Mail,MapPin,Package,Phone,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/companyLogoService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyLogoService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CompanyDetailsModal = (param)=>{\n    let { isOpen, onClose, companyId } = param;\n    _s();\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompanyDetailsModal.useEffect\": ()=>{\n            if (isOpen && companyId) {\n                loadCompanyDetails();\n            }\n        }\n    }[\"CompanyDetailsModal.useEffect\"], [\n        isOpen,\n        companyId\n    ]);\n    // Função para processar campos JSON que podem vir como string\n    const parseJsonField = function(jsonData) {\n        let fieldName = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'campo';\n        if (!jsonData) return {};\n        // Se já for um objeto, retornar como está\n        if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {\n            return jsonData;\n        }\n        // Se for uma string, tentar fazer o parse\n        if (typeof jsonData === 'string') {\n            try {\n                // Verificar se a string está vazia ou não é um JSON válido\n                if (!jsonData.trim() || !jsonData.startsWith('{') && !jsonData.startsWith('[')) {\n                    console.warn(\"[CompanyDetailsModal] \".concat(fieldName, \" n\\xe3o parece ser um JSON v\\xe1lido:\"), jsonData);\n                    return {};\n                }\n                return JSON.parse(jsonData);\n            } catch (error) {\n                console.error(\"[CompanyDetailsModal] Erro ao fazer parse do \".concat(fieldName, \":\"), error);\n                return {};\n            }\n        }\n        return {};\n    };\n    // Alias para manter compatibilidade\n    const parseSocialMedia = (data)=>parseJsonField(data, 'socialMedia');\n    const parseBusinessHours = (data)=>parseJsonField(data, 'businessHours');\n    const loadCompanyDetails = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log('[CompanyDetailsModal] Buscando empresa com ID:', companyId);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.api.get(\"/companies/\".concat(companyId));\n            console.log('[CompanyDetailsModal] Resposta da API:', response.data);\n            // Verificar se a empresa tem documentos/logo\n            if (response.data.documents && response.data.documents.length > 0 && response.data.documents[0] && response.data.documents[0].path) {\n                console.log('[CompanyDetailsModal] Documentos encontrados:', response.data.documents);\n                console.log('[CompanyDetailsModal] Caminho do logo:', response.data.documents[0].path);\n                // Tentar construir a URL do logo\n                const logoUrl = _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_4__.companyLogoService.getCompanyLogoUrl(response.data.id, response.data.documents[0].path);\n                console.log('[CompanyDetailsModal] URL do logo construída:', logoUrl);\n            } else {\n                console.log('[CompanyDetailsModal] Empresa não tem documentos/logo ou caminho inválido');\n            }\n            setCompany(response.data);\n        } catch (error) {\n            console.error('[CompanyDetailsModal] Erro ao carregar detalhes da empresa:', error);\n            setError('Não foi possível carregar os detalhes da empresa. Tente novamente mais tarde.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: \"Detalhes da Empresa\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-red-500 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, undefined) : company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex flex-col items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-32 w-32 rounded-lg bg-neutral-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden mb-4\",\n                                            children: company.documents && company.documents[0] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: company.documents && company.documents[0] && company.documents[0].path ? _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_4__.companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : '',\n                                                    alt: company.name,\n                                                    className: \"h-full w-full object-contain\",\n                                                    onLoad: ()=>console.log('[CompanyDetailsModal] Imagem carregada com sucesso'),\n                                                    onError: (e)=>{\n                                                        console.error('[CompanyDetailsModal] Erro ao carregar imagem:', e.target.src);\n                                                        e.target.onerror = null;\n                                                        e.target.style.display = 'none';\n                                                        e.target.parentNode.innerHTML = '<div class=\"h-16 w-16 text-neutral-400 dark:text-gray-500\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"20\" height=\"14\" x=\"2\" y=\"7\" rx=\"2\" ry=\"2\"/><path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"/></svg></div>';\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-16 w-16 text-neutral-400 dark:text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                lineNumber: 130,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-neutral-800 dark:text-white text-center\",\n                                            children: company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        company.tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-neutral-500 dark:text-gray-400 text-center\",\n                                            children: company.tradingName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 text-xs rounded-full\",\n                                            children: company.active ? \"Ativa\" : \"Inativa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"CNPJ:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.cnpj ? company.cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Telefone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.phone || \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        company.phone2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Telefone 2:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.phone2\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        company.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Website:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: company.website.startsWith('http') ? company.website : \"https://\".concat(company.website),\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"text-primary-600 dark:text-primary-400 font-medium hover:underline\",\n                                                                    children: company.website\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Endere\\xe7o\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Endere\\xe7o:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.address || \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"Cidade/Estado:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.city && company.state ? \"\".concat(company.city, \"/\").concat(company.state) : \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"CEP:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                    children: company.postalCode || \"N/A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, undefined),\n                        company.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                    children: \"Descri\\xe7\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-600 dark:text-gray-300 bg-neutral-50 dark:bg-gray-700 p-3 rounded-md\",\n                                    children: company.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 214,\n                            columnNumber: 15\n                        }, undefined),\n                        company.socialMedia && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                    children: \"Redes Sociais\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                                    children: Object.entries(parseSocialMedia(company.socialMedia) || {}).map((param)=>{\n                                        let [key, value] = param;\n                                        return value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: value.startsWith('http') ? value : \"https://\".concat(value),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Globe_Mail_MapPin_Package_Phone_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                (()=>{\n                                                    // Mapear nomes de redes sociais para exibição\n                                                    const socialNames = {\n                                                        facebook: 'Facebook',\n                                                        instagram: 'Instagram',\n                                                        linkedin: 'LinkedIn',\n                                                        twitter: 'Twitter',\n                                                        youtube: 'YouTube',\n                                                        website: 'Website',\n                                                        email: 'Email'\n                                                    };\n                                                    return socialNames[key] || key.charAt(0).toUpperCase() + key.slice(1);\n                                                })()\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 223,\n                            columnNumber: 15\n                        }, undefined),\n                        company.businessHours && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                    children: \"Hor\\xe1rio de Funcionamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                                    children: Object.entries(parseBusinessHours(company.businessHours) || {}).map((param)=>{\n                                        let [day, hours] = param;\n                                        return hours && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                    children: [\n                                                        day === 'monday' && 'Segunda-feira',\n                                                        day === 'tuesday' && 'Terça-feira',\n                                                        day === 'wednesday' && 'Quarta-feira',\n                                                        day === 'thursday' && 'Quinta-feira',\n                                                        day === 'friday' && 'Sexta-feira',\n                                                        day === 'saturday' && 'Sábado',\n                                                        day === 'sunday' && 'Domingo'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                    children: typeof hours === 'string' ? hours : hours.start && hours.end ? \"\".concat(hours.start, \" \\xe0s \").concat(hours.end) : JSON.stringify(hours)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, day, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 23\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                            lineNumber: 257,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-neutral-500 dark:text-neutral-400\",\n                    children: \"Nenhuma informa\\xe7\\xe3o dispon\\xedvel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end p-4 border-t border-neutral-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors\",\n                    children: \"Fechar\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyDetailsModal.js\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompanyDetailsModal, \"4yWIl/UcXWN+pzF47f2vnWUbQ9c=\");\n_c = CompanyDetailsModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyDetailsModal);\nvar _c;\n$RefreshReg$(_c, \"CompanyDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js\n"));

/***/ })

});