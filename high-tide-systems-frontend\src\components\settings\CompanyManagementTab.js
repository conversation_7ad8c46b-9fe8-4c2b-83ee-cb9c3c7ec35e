"use client";

import React, { useState, useEffect } from "react";
import {
  Building,
  Eye,
  Edit,
  Trash,
  Power,
  Search,
  Plus,
  Filter,
  RefreshCw,
  Globe,
  Mail,
  MapPin,
  Phone,
  Users,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { api } from "@/utils/api";
import { ModuleSelect, ModuleTable } from "@/components/ui";
import { useAuth } from "@/contexts/AuthContext";
import { companyLogoService } from "@/app/modules/admin/services/companyLogoService";
import CompanyFormModal from "./CompanyFormModal";
import CompanyDetailsModal from "./CompanyDetailsModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";

const CompanyManagementTab = () => {
  const [companies, setCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalCompanies, setTotalCompanies] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [companyModalOpen, setCompanyModalOpen] = useState(false);
  const [companyDetailsModalOpen, setCompanyDetailsModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const { user } = useAuth();

  // Check user roles
  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // If user is company admin or has a company ID but is not system admin, we should only show their company
  const isCompanyAdmin = user?.role === 'COMPANY_ADMIN' || (user?.companyId && user?.role !== 'SYSTEM_ADMIN');

  useEffect(() => {
    loadCompanies();
  }, []);

  const loadCompanies = async (page = currentPage) => {
    setIsLoading(true);
    try {
      // If company admin, just get their specific company
      if (isCompanyAdmin && user?.companyId) {
        const response = await api.get(`/companies/${user.companyId}`);
        if (response.data) {
          setCompanies([response.data]);
          setTotalCompanies(1);
          setTotalPages(1);
        } else {
          setCompanies([]);
          setTotalCompanies(0);
          setTotalPages(1);
        }
      } else {
        // System admin sees all companies
        const params = {
          page,
          limit: 10,
          search: search || undefined,
          active: statusFilter || undefined,
        };

        const response = await api.get("/companies", { params });

        setCompanies(response.data.companies || []);
        setTotalCompanies(response.data.total || 0);
        setTotalPages(response.data.pages || 1);
      }

      setCurrentPage(page);
    } catch (error) {
      console.error("Error loading companies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    loadCompanies(1);
  };

  const handleResetFilters = () => {
    setSearch("");
    setStatusFilter("");
    loadCompanies(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    loadCompanies(page);
  };

  const handleAddCompany = () => {
    setSelectedCompany(null);
    setCompanyModalOpen(true);
  };

  const handleEditCompany = (company) => {
    setSelectedCompany(company);
    setCompanyModalOpen(true);
  };

  const handleViewCompany = (companyId) => {
    setSelectedCompanyId(companyId);
    setCompanyDetailsModalOpen(true);
  };

  const handleToggleCompanyStatus = (company) => {
    if (!isSystemAdmin) return; // Only system admins can change status

    setSelectedCompany(company);
    setActionToConfirm({
      type: "toggle-status",
      message: `${company.active ? "Desativar" : "Ativar"} a empresa ${company.name}?`
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteCompany = (company) => {
    if (!isSystemAdmin) return; // Only system admins can delete

    setSelectedCompany(company);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente a empresa ${company.name}?`,
      variant: "danger"
    });
    setConfirmationDialogOpen(true);
  };

  const confirmAction = async () => {
    if (!selectedCompany || !isSystemAdmin) return;

    try {
      if (actionToConfirm.type === "toggle-status") {
        await api.patch(`/companies/${selectedCompany.id}/status`);
      } else if (actionToConfirm.type === "delete") {
        await api.delete(`/companies/${selectedCompany.id}`);
      }

      loadCompanies();
    } catch (error) {
      console.error("Error performing action:", error);
      alert(error.response?.data?.message || "Ocorreu um erro ao executar esta ação");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-neutral-800 dark:text-gray-100 flex items-center gap-2">
          <Building className="h-5 w-5 text-primary-500 dark:text-primary-400" />
          {isSystemAdmin ? "Gerenciamento de Empresas" : "Minha Empresa"}
        </h3>

        {isSystemAdmin && (
          <button
            onClick={handleAddCompany}
            className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Nova Empresa</span>
          </button>
        )}
      </div>

      {/* Filters - only for system admin viewing multiple companies */}
      {isSystemAdmin && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-4">
          <form
            onSubmit={handleSearch}
            className="flex flex-col md:flex-row gap-4"
          >
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
              <input
                type="text"
                placeholder="Buscar por nome, nome fantasia ou CNPJ..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <ModuleSelect
                moduleColor="admin"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">Todos os status</option>
                <option value="true">Ativas</option>
                <option value="false">Inativas</option>
              </ModuleSelect>

              <button
                type="submit"
                className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
              >
                <Filter className="sm:hidden h-5 w-5" />
                <span className="hidden sm:inline">Filtrar</span>
              </button>

              <button
                type="button"
                onClick={handleResetFilters}
                className="px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
              >
                <RefreshCw className="sm:hidden h-5 w-5" />
                <span className="hidden sm:inline">Limpar</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Companies List or Single Company Card */}
      <div className="space-y-6">
        {isLoading ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
          </div>
        ) : companies.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 text-center">
            <p className="text-neutral-500 dark:text-gray-400">Nenhuma empresa encontrada</p>
          </div>
        ) : isCompanyAdmin ? (
          // Company Admin sees just their company as a card
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-4">
                  <div className="h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <Building className="h-8 w-8" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-neutral-800 dark:text-white">{companies[0].name}</h3>
                    {companies[0].tradingName && (
                      <p className="text-sm text-neutral-600 dark:text-gray-300">{companies[0].tradingName}</p>
                    )}
                    <div className="flex items-center mt-1 text-xs text-neutral-500 dark:text-gray-400">
                      <span className={`px-2 py-0.5 rounded-full ${
                        companies[0].active ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300" : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                      }`}>
                        {companies[0].active ? "Ativa" : "Inativa"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleViewCompany(companies[0].id)}
                    className="px-3 py-1.5 bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-md hover:bg-neutral-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-1.5"
                  >
                    <Eye className="h-4 w-4" />
                    <span>Detalhes</span>
                  </button>
                  <button
                    onClick={() => handleEditCompany(companies[0])}
                    className="px-3 py-1.5 bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-md hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors flex items-center gap-1.5"
                  >
                    <Edit className="h-4 w-4" />
                    <span>Editar</span>
                  </button>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-8">
                <div>
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5">
                    <Building className="h-4 w-4 text-neutral-500 dark:text-gray-400" />
                    Informações Básicas
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">CNPJ:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">
                        {companies[0].cnpj ? companies[0].cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, "$1.$2.$3/$4-$5") : "N/A"}
                      </span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Telefone:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].phone || "N/A"}</span>
                    </p>
                    {companies[0].phone2 && (
                      <p className="text-sm flex justify-between">
                        <span className="text-neutral-500 dark:text-gray-400">Telefone 2:</span>
                        <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].phone2}</span>
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-neutral-500 dark:text-gray-400" />
                    Endereço
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Endereço:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].address || "N/A"}</span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Cidade/Estado:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">
                        {companies[0].city && companies[0].state
                          ? `${companies[0].city}, ${companies[0].state}`
                          : "N/A"}
                      </span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">CEP:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].postalCode || "N/A"}</span>
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5">
                    <Globe className="h-4 w-4 text-neutral-500 dark:text-gray-400" />
                    Online
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Website:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].website || "N/A"}</span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Email:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].contactEmail || "N/A"}</span>
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5">
                    <Mail className="h-4 w-4 text-neutral-500 dark:text-gray-400" />
                    Outras Informações
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Moeda:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].defaultCurrency || "BRL"}</span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Fuso Horário:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{companies[0].timeZone || "America/Sao_Paulo"}</span>
                    </p>
                    {companies[0].description && (
                      <p className="text-sm mt-3">
                        <span className="text-neutral-500 dark:text-gray-400 block mb-1">Descrição:</span>
                        <span className="text-neutral-800 dark:text-gray-200">{companies[0].description}</span>
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // System Admin sees a table of all companies
          <ModuleTable
            moduleColor="admin"
            title="Empresas"
            columns={[
              { header: 'Empresa', field: 'name', width: '20%' },
              { header: 'CNPJ', field: 'cnpj', width: '15%' },
              { header: 'Plano', field: 'plan', width: '15%' },
              { header: 'Pagamento', field: 'payment', width: '15%' },
              { header: 'Usuários', field: 'users', width: '10%' },
              { header: 'Status', field: 'status', width: '10%' },
              { header: 'Ações', field: 'actions', width: '15%', sortable: false }
            ]}
            data={companies}
            isLoading={isLoading}
            emptyMessage="Nenhuma empresa encontrada"
            emptyIcon={<Building size={24} />}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalCompanies}
            onPageChange={handlePageChange}
            showPagination={totalPages > 1}
            tableId="admin-companies-table"
            enableColumnToggle={true}
            renderRow={(company, index, moduleColors, visibleColumns) => (
              <tr key={company.id} className={moduleColors.hoverBg}>
                {visibleColumns.includes('name') && (
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                        {company.documents && company.documents[0] ? (
                          <>
                            {/* Testar diferentes URLs para ver qual funciona */}
                            <img
                              className="h-10 w-10 rounded-full object-cover border border-neutral-200 dark:border-gray-600"
                              src={company.documents && company.documents[0] && company.documents[0].path ? companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : ''}
                              alt={company.name}
                              onLoad={() => console.log(`[CompanyManagementTab] Imagem da empresa ${company.id} carregada com sucesso`)}
                              onError={(e) => {
                                console.error(`[CompanyManagementTab] Erro ao carregar imagem da empresa ${company.id}:`, e.target.src);
                                e.target.onerror = null;
                                e.target.style.display = 'none';
                                e.target.parentNode.innerHTML = `<div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg></div>`;
                              }}
                            />
                          </>
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400">
                            <Building className="h-5 w-5" />
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                          {company.name}
                        </div>
                        {company.tradingName && (
                          <div className="text-xs text-neutral-500 dark:text-neutral-400">
                            {company.tradingName}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                )}
                {visibleColumns.includes('cnpj') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    {company.cnpj
                      ? company.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, "$1.$2.$3/$4-$5")
                      : "N/A"}
                  </td>
                )}
                {visibleColumns.includes('contact') && (
                  <td className="px-4 py-4">
                    {company.phone && (
                      <div className="text-sm text-neutral-600 dark:text-neutral-300 flex items-center">
                        <Phone className="h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                        {company.phone}
                      </div>
                    )}
                    {company.contactEmail && (
                      <div className="text-sm text-neutral-600 dark:text-neutral-300 flex items-center mt-1">
                        <Mail className="h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                        {company.contactEmail}
                      </div>
                    )}
                  </td>
                )}
                {visibleColumns.includes('status') && (
                  <td className="px-4 py-4">
                    <span
                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        company.active
                          ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                          : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                      }`}
                    >
                      {company.active ? "Ativa" : "Inativa"}
                    </span>
                  </td>
                )}
                {visibleColumns.includes('actions') && (
                  <td className="px-4 py-4 text-right">
                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => handleViewCompany(company.id)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                        title="Visualizar"
                      >
                        <Eye size={16} />
                      </button>

                      <button
                        onClick={() => handleEditCompany(company)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                        title="Editar"
                      >
                        <Edit size={16} />
                      </button>

                      <button
                        onClick={() => handleToggleCompanyStatus(company)}
                        className={`p-1 transition-colors ${
                          company.active
                            ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                            : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                        }`}
                        title={company.active ? "Desativar" : "Ativar"}
                      >
                        <Power size={16} />
                      </button>

                      <button
                        onClick={() => handleDeleteCompany(company)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                        title="Excluir"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </td>
                )}
              </tr>
            )}
          />
        )}
      </div>

      {/* Modals */}
      {companyModalOpen && (
        <CompanyFormModal
          isOpen={companyModalOpen}
          onClose={() => setCompanyModalOpen(false)}
          company={selectedCompany}
          onSuccess={() => {
            setCompanyModalOpen(false);
            loadCompanies();
          }}
        />
      )}

      <CompanyDetailsModal
        isOpen={companyDetailsModalOpen}
        onClose={() => setCompanyDetailsModalOpen(false)}
        companyId={selectedCompanyId}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        variant={actionToConfirm?.variant || "warning"}
      />
    </div>
  );
};

export default CompanyManagementTab;