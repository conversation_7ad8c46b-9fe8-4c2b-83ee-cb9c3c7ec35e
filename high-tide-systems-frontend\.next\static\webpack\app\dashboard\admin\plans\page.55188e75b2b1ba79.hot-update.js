"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/components/settings/CompanyManagementTab.js":
/*!*********************************************************!*\
  !*** ./src/components/settings/CompanyManagementTab.js ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyLogoService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyLogoService.js\");\n/* harmony import */ var _CompanyFormModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CompanyFormModal */ \"(app-pages-browser)/./src/components/settings/CompanyFormModal.js\");\n/* harmony import */ var _CompanyDetailsModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CompanyDetailsModal */ \"(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CompanyManagementTab = ()=>{\n    _s();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalCompanies, setTotalCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyModalOpen, setCompanyModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyDetailsModalOpen, setCompanyDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // Check user roles\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // If user is company admin or has a company ID but is not system admin, we should only show their company\n    const isCompanyAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'COMPANY_ADMIN' || (user === null || user === void 0 ? void 0 : user.companyId) && (user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompanyManagementTab.useEffect\": ()=>{\n            loadCompanies();\n        }\n    }[\"CompanyManagementTab.useEffect\"], []);\n    const loadCompanies = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage;\n        setIsLoading(true);\n        try {\n            // If company admin, just get their specific company\n            if (isCompanyAdmin && (user === null || user === void 0 ? void 0 : user.companyId)) {\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies/\".concat(user.companyId));\n                if (response.data) {\n                    setCompanies([\n                        response.data\n                    ]);\n                    setTotalCompanies(1);\n                    setTotalPages(1);\n                } else {\n                    setCompanies([]);\n                    setTotalCompanies(0);\n                    setTotalPages(1);\n                }\n            } else {\n                // System admin sees all companies\n                const params = {\n                    page,\n                    limit: 10,\n                    search: search || undefined,\n                    active: statusFilter || undefined\n                };\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies\", {\n                    params\n                });\n                setCompanies(response.data.companies || []);\n                setTotalCompanies(response.data.total || 0);\n                setTotalPages(response.data.pages || 1);\n            }\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Error loading companies:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        loadCompanies(1);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        loadCompanies(1);\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        loadCompanies(page);\n    };\n    const handleAddCompany = ()=>{\n        setSelectedCompany(null);\n        setCompanyModalOpen(true);\n    };\n    const handleEditCompany = (company)=>{\n        setSelectedCompany(company);\n        setCompanyModalOpen(true);\n    };\n    const handleViewCompany = (companyId)=>{\n        setSelectedCompanyId(companyId);\n        setCompanyDetailsModalOpen(true);\n    };\n    const handleToggleCompanyStatus = (company)=>{\n        if (!isSystemAdmin) return; // Only system admins can change status\n        setSelectedCompany(company);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(company.active ? \"Desativar\" : \"Ativar\", \" a empresa \").concat(company.name, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteCompany = (company)=>{\n        if (!isSystemAdmin) return; // Only system admins can delete\n        setSelectedCompany(company);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente a empresa \".concat(company.name, \"?\"),\n            variant: \"danger\"\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const confirmAction = async ()=>{\n        if (!selectedCompany || !isSystemAdmin) return;\n        try {\n            if (actionToConfirm.type === \"toggle-status\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.patch(\"/companies/\".concat(selectedCompany.id, \"/status\"));\n            } else if (actionToConfirm.type === \"delete\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.delete(\"/companies/\".concat(selectedCompany.id));\n            }\n            loadCompanies();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error performing action:\", error);\n            alert(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Ocorreu um erro ao executar esta ação\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-100 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            isSystemAdmin ? \"Gerenciamento de Empresas\" : \"Minha Empresa\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleAddCompany,\n                        className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Nova Empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome, nome fantasia ou CNPJ...\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-500 dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"true\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"false\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"sm:hidden h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    className: \"px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"sm:hidden h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, undefined) : companies.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-500 dark:text-gray-400\",\n                        children: \"Nenhuma empresa encontrada\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, undefined) : isCompanyAdmin ? // Company Admin sees just their company as a card\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-neutral-800 dark:text-white\",\n                                                        children: companies[0].name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                                        children: companies[0].tradingName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-0.5 rounded-full \".concat(companies[0].active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                                            children: companies[0].active ? \"Ativa\" : \"Inativa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewCompany(companies[0].id),\n                                                className: \"px-3 py-1.5 bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-md hover:bg-neutral-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Detalhes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditCompany(companies[0]),\n                                                className: \"px-3 py-1.5 bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-md hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Editar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"CNPJ:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].cnpj ? companies[0].cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Telefone:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].phone || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].phone2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Telefone 2:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].phone2\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Endere\\xe7o\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Endere\\xe7o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].address || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Cidade/Estado:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].city && companies[0].state ? \"\".concat(companies[0].city, \", \").concat(companies[0].state) : \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"CEP:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].postalCode || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Online\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Website:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].website || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Email:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].contactEmail || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Outras Informa\\xe7\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Moeda:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].defaultCurrency || \"BRL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Fuso Hor\\xe1rio:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].timeZone || \"America/Sao_Paulo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400 block mb-1\",\n                                                                children: \"Descri\\xe7\\xe3o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200\",\n                                                                children: companies[0].description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 251,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, undefined) : // System Admin sees a table of all companies\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Empresas\",\n                    columns: [\n                        {\n                            header: 'Empresa',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'CNPJ',\n                            field: 'cnpj',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Plano',\n                            field: 'plan',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Pagamento',\n                            field: 'payment',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'status',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: companies,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma empresa encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 399,\n                        columnNumber: 24\n                    }, void 0),\n                    currentPage: currentPage,\n                    totalPages: totalPages,\n                    totalItems: totalCompanies,\n                    onPageChange: handlePageChange,\n                    showPagination: totalPages > 1,\n                    tableId: \"admin-companies-table\",\n                    enableColumnToggle: true,\n                    renderRow: (company, index, moduleColors, visibleColumns)=>{\n                        var _company_subscriptionInfo, _company_subscriptionInfo1, _company_subscriptionInfo2, _company_subscriptionInfo3, _company_subscriptionInfo4, _company_subscriptionInfo5, _company_subscriptionInfo6, _company_subscriptionInfo7, _company_subscriptionInfo8;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 flex items-center justify-center\",\n                                                children: company.documents && company.documents[0] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-10 w-10 rounded-full object-cover border border-neutral-200 dark:border-gray-600\",\n                                                        src: company.documents && company.documents[0] && company.documents[0].path ? _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__.companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : '',\n                                                        alt: company.name,\n                                                        onLoad: ()=>console.log(\"[CompanyManagementTab] Imagem da empresa \".concat(company.id, \" carregada com sucesso\")),\n                                                        onError: (e)=>{\n                                                            console.error(\"[CompanyManagementTab] Erro ao carregar imagem da empresa \".concat(company.id, \":\"), e.target.src);\n                                                            e.target.onerror = null;\n                                                            e.target.style.display = 'none';\n                                                            e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"20\" height=\"14\" x=\"2\" y=\"7\" rx=\"2\" ry=\"2\"/><path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"/></svg></div>';\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 412,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100\",\n                                                        children: company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    company.tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-neutral-500 dark:text-neutral-400\",\n                                                        children: company.tradingName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 435,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 411,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 410,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('cnpj') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                    children: company.cnpj ? company.cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 449,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('plan') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-neutral-800 dark:text-neutral-100\",\n                                                children: ((_company_subscriptionInfo = company.subscriptionInfo) === null || _company_subscriptionInfo === void 0 ? void 0 : _company_subscriptionInfo.planName) || 'Sem Plano'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 458,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-neutral-500 dark:text-neutral-400\",\n                                                children: [\n                                                    ((_company_subscriptionInfo1 = company.subscriptionInfo) === null || _company_subscriptionInfo1 === void 0 ? void 0 : _company_subscriptionInfo1.moduleCount) || 0,\n                                                    \" m\\xf3dulos\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 461,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 457,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 456,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('payment') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            ((_company_subscriptionInfo2 = company.subscriptionInfo) === null || _company_subscriptionInfo2 === void 0 ? void 0 : _company_subscriptionInfo2.paymentStatus) === 'up_to_date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-600 dark:text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Em dia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 471,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo3 = company.subscriptionInfo) === null || _company_subscriptionInfo3 === void 0 ? void 0 : _company_subscriptionInfo3.paymentStatus) === 'overdue' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-red-600 dark:text-red-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Atrasado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 477,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo4 = company.subscriptionInfo) === null || _company_subscriptionInfo4 === void 0 ? void 0 : _company_subscriptionInfo4.paymentStatus) === 'expired' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-orange-600 dark:text-orange-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Vencido\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 483,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo5 = company.subscriptionInfo) === null || _company_subscriptionInfo5 === void 0 ? void 0 : _company_subscriptionInfo5.isNearExpiry) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-yellow-600 dark:text-yellow-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Pr\\xf3x. venc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo6 = company.subscriptionInfo) === null || _company_subscriptionInfo6 === void 0 ? void 0 : _company_subscriptionInfo6.paymentStatus) === 'no_subscription' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Sem plano\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 469,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 468,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_company_subscriptionInfo7 = company.subscriptionInfo) === null || _company_subscriptionInfo7 === void 0 ? void 0 : _company_subscriptionInfo7.userCount) || 0,\n                                                    ((_company_subscriptionInfo8 = company.subscriptionInfo) === null || _company_subscriptionInfo8 === void 0 ? void 0 : _company_subscriptionInfo8.userLimit) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-400 dark:text-neutral-500\",\n                                                        children: [\n                                                            \"/\",\n                                                            company.subscriptionInfo.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 505,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 504,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('contact') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: [\n                                        company.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-neutral-600 dark:text-neutral-300 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                company.phone\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 521,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        company.contactEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-neutral-600 dark:text-neutral-300 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                company.contactEmail\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 527,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 519,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(company.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                        children: company.active ? \"Ativa\" : \"Inativa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 536,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 535,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4 text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewCompany(company.id),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                title: \"Visualizar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 550,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditCompany(company),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 558,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleCompanyStatus(company),\n                                                className: \"p-1 transition-colors \".concat(company.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                title: company.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 566,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteCompany(company),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 578,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 549,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 548,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 408,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 384,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined),\n            companyModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompanyFormModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: companyModalOpen,\n                onClose: ()=>setCompanyModalOpen(false),\n                company: selectedCompany,\n                onSuccess: ()=>{\n                    setCompanyModalOpen(false);\n                    loadCompanies();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 596,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompanyDetailsModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: companyDetailsModalOpen,\n                onClose: ()=>setCompanyDetailsModalOpen(false),\n                companyId: selectedCompanyId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.variant) || \"warning\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 613,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompanyManagementTab, \"9dHN0xgRBQS8/O5mdeEH5tqyYdQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = CompanyManagementTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyManagementTab);\nvar _c;\n$RefreshReg$(_c, \"CompanyManagementTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/CompanyManagementTab.js\n"));

/***/ })

});