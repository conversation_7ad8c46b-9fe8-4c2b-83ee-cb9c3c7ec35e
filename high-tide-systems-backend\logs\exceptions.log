{"timestamp":"2025-06-16 17:48:40","level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/admin/companyRoutes.js:22:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:15:23)","error":{},"stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/admin/companyRoutes.js:22:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:15:23)","exception":true,"date":"Mon Jun 16 2025 17:48:40 GMT+0000 (Coordinated Universal Time)","process":{"pid":2830,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":*********,"heapTotal":81780736,"heapUsed":62236440,"external":2210969,"arrayBuffers":47505}},"os":{"loadavg":[2.88,2.1,1.87],"uptime":17049.05},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"/usr/src/app/src/routes/admin/companyRoutes.js","function":null,"line":22,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":23,"file":"/usr/src/app/src/server.js","function":null,"line":15,"method":null,"native":false}]}
{"timestamp":"2025-06-16 17:48:58","level":"error","message":"uncaughtException: Route.post() requires a callback function but got a [object Undefined]\nError: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/admin/companyRoutes.js:22:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:15:23)","error":{},"stack":"Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/admin/companyRoutes.js:22:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:15:23)","exception":true,"date":"Mon Jun 16 2025 17:48:58 GMT+0000 (Coordinated Universal Time)","process":{"pid":2860,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":*********,"heapTotal":81518592,"heapUsed":62540272,"external":2210969,"arrayBuffers":47505}},"os":{"loadavg":[2.72,2.12,1.88],"uptime":17066.93},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as post]","line":216,"method":"<computed> [as post]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as post]","line":521,"method":"<computed> [as post]","native":false},{"column":8,"file":"/usr/src/app/src/routes/admin/companyRoutes.js","function":null,"line":22,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":23,"file":"/usr/src/app/src/server.js","function":null,"line":15,"method":null,"native":false}]}
