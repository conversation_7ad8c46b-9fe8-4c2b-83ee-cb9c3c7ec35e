'use client';

import React, { useEffect, useState } from 'react';
import Modal from '@/components/ui/Modal';
import { useAuth } from '@/contexts/AuthContext';
import companyLogoService from '@/services/companyLogoService';
import {
  Building,
  Globe,
  Mail,
  MapPin,
  Phone,
  X,
  CreditCard,
  Users,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Package,
  DollarSign,
  FileText,
  Settings,
  Activity,
  TrendingUp,
  Shield,
  Database,
  Zap,
  BarChart3,
  Download,
  ExternalLink,
  Info,
  Star,
  Award,
  Target
} from 'lucide-react';
import { api } from '@/utils/api';
import { companyLogoService } from '@/app/modules/admin/services/companyLogoService';

const CompanyDetailsModal = ({ isOpen, onClose, companyId }) => {
  const { user } = useAuth();
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Funções auxiliares para parsing de dados
  const parseSocialMedia = (socialMediaString) => {
    if (!socialMediaString) return {};
    try {
      return typeof socialMediaString === 'string'
        ? JSON.parse(socialMediaString)
        : socialMediaString;
    } catch {
      return {};
    }
  };

  const parseBusinessHours = (businessHoursString) => {
    if (!businessHoursString) return {};
    try {
      return typeof businessHoursString === 'string'
        ? JSON.parse(businessHoursString)
        : businessHoursString;
    } catch {
      return {};
    }
  };

  useEffect(() => {
    if (isOpen && companyId) {
      loadCompanyDetails();
    }
  }, [isOpen, companyId]);

  // Função para processar campos JSON que podem vir como string
  const parseJsonField = (jsonData, fieldName = 'campo') => {
    if (!jsonData) return {};

    // Se já for um objeto, retornar como está
    if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {
      return jsonData;
    }

    // Se for uma string, tentar fazer o parse
    if (typeof jsonData === 'string') {
      try {
        // Verificar se a string está vazia ou não é um JSON válido
        if (!jsonData.trim() || (!jsonData.startsWith('{') && !jsonData.startsWith('['))) {
          console.warn(`[CompanyDetailsModal] ${fieldName} não parece ser um JSON válido:`, jsonData);
          return {};
        }
        return JSON.parse(jsonData);
      } catch (error) {
        console.error(`[CompanyDetailsModal] Erro ao fazer parse do ${fieldName}:`, error);
        return {};
      }
    }

    return {};
  };

  // Alias para manter compatibilidade
  const parseSocialMedia = (data) => parseJsonField(data, 'socialMedia');
  const parseBusinessHours = (data) => parseJsonField(data, 'businessHours');

  const loadCompanyDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('[CompanyDetailsModal] Buscando empresa com ID:', companyId);
      const response = await api.get(`/companies/${companyId}`);
      console.log('[CompanyDetailsModal] Resposta da API:', response.data);

      // Verificar se a empresa tem documentos/logo
      if (response.data.documents && response.data.documents.length > 0 && response.data.documents[0] && response.data.documents[0].path) {
        console.log('[CompanyDetailsModal] Documentos encontrados:', response.data.documents);
        console.log('[CompanyDetailsModal] Caminho do logo:', response.data.documents[0].path);

        // Tentar construir a URL do logo
        const logoUrl = companyLogoService.getCompanyLogoUrl(response.data.id, response.data.documents[0].path);
        console.log('[CompanyDetailsModal] URL do logo construída:', logoUrl);
      } else {
        console.log('[CompanyDetailsModal] Empresa não tem documentos/logo ou caminho inválido');
      }

      setCompany(response.data);
    } catch (error) {
      console.error('[CompanyDetailsModal] Erro ao carregar detalhes da empresa:', error);
      setError('Não foi possível carregar os detalhes da empresa. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  // Definir abas disponíveis baseado no role do usuário
  const tabs = [
    { id: 'overview', label: 'Visão Geral', icon: Building },
    { id: 'contact', label: 'Contato', icon: Phone },
    ...(isSystemAdmin ? [
      { id: 'subscription', label: 'Assinatura', icon: CreditCard },
      { id: 'users', label: 'Usuários', icon: Users },
      { id: 'activity', label: 'Atividade', icon: Activity }
    ] : []),
    { id: 'settings', label: 'Configurações', icon: Settings }
  ];

  // Função para formatar valores monetários
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0);
  };

  // Função para formatar datas
  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Função para formatar data e hora
  const formatDateTime = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Detalhes da Empresa" size="xl">
      <div className="flex flex-col h-full max-h-[90vh]">
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12 text-red-500 dark:text-red-400">
            <XCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">Erro ao carregar dados</p>
            <p className="text-sm opacity-75">{error}</p>
          </div>
        ) : company ? (
          <>
            {/* Cabeçalho da Empresa */}
            <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 p-6 border-b border-neutral-200 dark:border-gray-700">
              <div className="flex items-start gap-6">
                {/* Logo */}
                <div className="flex-shrink-0">
                  <div className="h-20 w-20 rounded-xl bg-white dark:bg-gray-800 shadow-lg flex items-center justify-center overflow-hidden border border-neutral-200 dark:border-gray-600">
                    {company.documents && company.documents[0] ? (
                      <img
                        src={companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path)}
                        alt={company.name}
                        className="h-full w-full object-contain"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.parentNode.innerHTML = `<div class="h-12 w-12 text-neutral-400 dark:text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg></div>`;
                        }}
                      />
                    ) : (
                      <Building className="h-12 w-12 text-neutral-400 dark:text-gray-500" />
                    )}
                  </div>
                </div>

                {/* Informações Principais */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="text-2xl font-bold text-neutral-900 dark:text-white mb-1">
                        {company.name}
                      </h2>
                      {company.tradingName && (
                        <p className="text-lg text-neutral-600 dark:text-gray-300 mb-2">
                          {company.tradingName}
                        </p>
                      )}
                      <div className="flex items-center gap-4 text-sm text-neutral-500 dark:text-gray-400">
                        <span>CNPJ: {company.cnpj ? company.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, "$1.$2.$3/$4-$5") : "N/A"}</span>
                        <span>•</span>
                        <span>Criada em {formatDate(company.createdAt)}</span>
                      </div>
                    </div>

                    {/* Status e Badges */}
                    <div className="flex flex-col items-end gap-2">
                      <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                        company.active
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                      }`}>
                        {company.active ? 'Ativa' : 'Inativa'}
                      </div>

                      {company.subscriptionInfo && (
                        <div className="flex items-center gap-2">
                          {company.subscriptionInfo.paymentStatus === 'up_to_date' && (
                            <div className="flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">
                              <CheckCircle className="h-3 w-3" />
                              Pagamentos em dia
                            </div>
                          )}
                          {company.subscriptionInfo.paymentStatus === 'overdue' && (
                            <div className="flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full text-xs">
                              <XCircle className="h-3 w-3" />
                              Pagamento atrasado
                            </div>
                          )}
                          {company.subscriptionInfo.isNearExpiry && (
                            <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-xs">
                              <Clock className="h-3 w-3" />
                              Próximo ao vencimento
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Métricas Rápidas */}
                  {company.subscriptionInfo && (
                    <div className="mt-4 grid grid-cols-3 gap-4">
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-neutral-200 dark:border-gray-600">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-blue-500" />
                          <span className="text-sm text-neutral-600 dark:text-gray-400">Plano</span>
                        </div>
                        <p className="text-lg font-semibold text-neutral-900 dark:text-white mt-1">
                          {company.subscriptionInfo.planName}
                        </p>
                        <p className="text-xs text-neutral-500 dark:text-gray-400">
                          {company.subscriptionInfo.moduleCount} módulos
                        </p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-neutral-200 dark:border-gray-600">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-purple-500" />
                          <span className="text-sm text-neutral-600 dark:text-gray-400">Usuários</span>
                        </div>
                        <p className="text-lg font-semibold text-neutral-900 dark:text-white mt-1">
                          {company.subscriptionInfo.userCount}
                          {company.subscriptionInfo.userLimit > 0 && (
                            <span className="text-sm text-neutral-500 dark:text-gray-400">
                              /{company.subscriptionInfo.userLimit}
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-neutral-500 dark:text-gray-400">
                          {company.subscriptionInfo.userUsagePercentage}% utilizado
                        </p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-neutral-200 dark:border-gray-600">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-neutral-600 dark:text-gray-400">Mensalidade</span>
                        </div>
                        <p className="text-lg font-semibold text-neutral-900 dark:text-white mt-1">
                          {formatCurrency(company.subscriptionInfo.pricePerMonth)}
                        </p>
                        <p className="text-xs text-neutral-500 dark:text-gray-400">
                          {company.subscriptionInfo.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Navegação por Abas */}
            <div className="border-b border-neutral-200 dark:border-gray-700 bg-neutral-50 dark:bg-gray-800/50">
              <nav className="flex space-x-8 px-6" aria-label="Tabs">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                        activeTab === tab.id
                          ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                          : 'border-transparent text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300 hover:border-neutral-300 dark:hover:border-gray-600'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Conteúdo das Abas */}
            <div className="flex-1 overflow-y-auto p-6">
              {/* Aba: Visão Geral */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Informações Básicas */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Building className="h-5 w-5 text-blue-500" />
                        Informações da Empresa
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-neutral-500 dark:text-gray-400">Nome Legal:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.legalName || company.name}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-neutral-500 dark:text-gray-400">Nome Fantasia:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.tradingName || 'N/A'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-neutral-500 dark:text-gray-400">Setor:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.industry || 'Não informado'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-neutral-500 dark:text-gray-400">Moeda Padrão:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.defaultCurrency || 'BRL'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-neutral-500 dark:text-gray-400">Fuso Horário:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.timeZone || 'America/Sao_Paulo'}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-green-500" />
                        Localização
                      </h3>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm text-neutral-500 dark:text-gray-400 block">Endereço:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.address || 'Não informado'}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <span className="text-sm text-neutral-500 dark:text-gray-400 block">Cidade:</span>
                            <span className="text-sm font-medium text-neutral-900 dark:text-white">
                              {company.city || 'N/A'}
                            </span>
                          </div>
                          <div>
                            <span className="text-sm text-neutral-500 dark:text-gray-400 block">Estado:</span>
                            <span className="text-sm font-medium text-neutral-900 dark:text-white">
                              {company.state || 'N/A'}
                            </span>
                          </div>
                        </div>
                        <div>
                          <span className="text-sm text-neutral-500 dark:text-gray-400 block">CEP:</span>
                          <span className="text-sm font-medium text-neutral-900 dark:text-white">
                            {company.postalCode || 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Descrição */}
                  {company.description && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <FileText className="h-5 w-5 text-purple-500" />
                        Sobre a Empresa
                      </h3>
                      <p className="text-sm text-neutral-600 dark:text-gray-300 leading-relaxed">
                        {company.description}
                      </p>
                    </div>
                  )}

                  {/* Cores da Marca */}
                  {(company.primaryColor || company.secondaryColor) && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Star className="h-5 w-5 text-yellow-500" />
                        Identidade Visual
                      </h3>
                      <div className="flex items-center gap-6">
                        {company.primaryColor && (
                          <div className="flex items-center gap-3">
                            <div
                              className="w-12 h-12 rounded-lg border border-neutral-200 dark:border-gray-600 shadow-sm"
                              style={{ backgroundColor: company.primaryColor }}
                            ></div>
                            <div>
                              <p className="text-sm font-medium text-neutral-900 dark:text-white">Cor Primária</p>
                              <p className="text-xs text-neutral-500 dark:text-gray-400 font-mono">
                                {company.primaryColor}
                              </p>
                            </div>
                          </div>
                        )}
                        {company.secondaryColor && (
                          <div className="flex items-center gap-3">
                            <div
                              className="w-12 h-12 rounded-lg border border-neutral-200 dark:border-gray-600 shadow-sm"
                              style={{ backgroundColor: company.secondaryColor }}
                            ></div>
                            <div>
                              <p className="text-sm font-medium text-neutral-900 dark:text-white">Cor Secundária</p>
                              <p className="text-xs text-neutral-500 dark:text-gray-400 font-mono">
                                {company.secondaryColor}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Aba: Contato */}
              {activeTab === 'contact' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Informações de Contato */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Phone className="h-5 w-5 text-blue-500" />
                        Contato
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <Phone className="h-4 w-4 text-neutral-400 dark:text-gray-500" />
                          <div>
                            <p className="text-sm text-neutral-500 dark:text-gray-400">Telefone Principal</p>
                            <p className="text-sm font-medium text-neutral-900 dark:text-white">
                              {company.phone || 'Não informado'}
                            </p>
                          </div>
                        </div>

                        {company.phone2 && (
                          <div className="flex items-center gap-3">
                            <Phone className="h-4 w-4 text-neutral-400 dark:text-gray-500" />
                            <div>
                              <p className="text-sm text-neutral-500 dark:text-gray-400">Telefone Secundário</p>
                              <p className="text-sm font-medium text-neutral-900 dark:text-white">
                                {company.phone2}
                              </p>
                            </div>
                          </div>
                        )}

                        <div className="flex items-center gap-3">
                          <Mail className="h-4 w-4 text-neutral-400 dark:text-gray-500" />
                          <div>
                            <p className="text-sm text-neutral-500 dark:text-gray-400">Email de Contato</p>
                            <p className="text-sm font-medium text-neutral-900 dark:text-white">
                              {company.contactEmail || 'Não informado'}
                            </p>
                          </div>
                        </div>

                        {company.website && (
                          <div className="flex items-center gap-3">
                            <Globe className="h-4 w-4 text-neutral-400 dark:text-gray-500" />
                            <div>
                              <p className="text-sm text-neutral-500 dark:text-gray-400">Website</p>
                              <a
                                href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1"
                              >
                                {company.website}
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Políticas e Termos */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Shield className="h-5 w-5 text-green-500" />
                        Políticas e Termos
                      </h3>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-neutral-500 dark:text-gray-400 mb-2">Política de Privacidade</p>
                          {company.privacyPolicyUrl ? (
                            <a
                              href={company.privacyPolicyUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1"
                            >
                              Ver Política de Privacidade
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          ) : (
                            <p className="text-sm text-neutral-400 dark:text-gray-500">Não informado</p>
                          )}
                        </div>

                        <div>
                          <p className="text-sm text-neutral-500 dark:text-gray-400 mb-2">Termos de Serviço</p>
                          {company.termsOfServiceUrl ? (
                            <a
                              href={company.termsOfServiceUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1"
                            >
                              Ver Termos de Serviço
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          ) : (
                            <p className="text-sm text-neutral-400 dark:text-gray-500">Não informado</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Redes Sociais */}
                  {company.socialMedia && Object.keys(parseSocialMedia(company.socialMedia)).length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Globe className="h-5 w-5 text-purple-500" />
                        Redes Sociais
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {Object.entries(parseSocialMedia(company.socialMedia) || {}).map(([key, value]) => (
                          value && (
                            <a
                              key={key}
                              href={value.startsWith('http') ? value : `https://${value}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-3 p-3 bg-neutral-50 dark:bg-gray-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-gray-600 transition-colors"
                            >
                              <Globe className="h-4 w-4 text-primary-500" />
                              <div>
                                <p className="text-sm font-medium text-neutral-900 dark:text-white">
                                  {(() => {
                                    const socialNames = {
                                      facebook: 'Facebook',
                                      instagram: 'Instagram',
                                      linkedin: 'LinkedIn',
                                      twitter: 'Twitter',
                                      youtube: 'YouTube',
                                      website: 'Website',
                                      email: 'Email'
                                    };
                                    return socialNames[key] || key.charAt(0).toUpperCase() + key.slice(1);
                                  })()}
                                </p>
                                <p className="text-xs text-neutral-500 dark:text-gray-400 truncate">
                                  {value}
                                </p>
                              </div>
                              <ExternalLink className="h-3 w-3 text-neutral-400 dark:text-gray-500 ml-auto" />
                            </a>
                          )
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Horário de Funcionamento */}
                  {company.businessHours && Object.keys(parseBusinessHours(company.businessHours)).length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Clock className="h-5 w-5 text-orange-500" />
                        Horário de Funcionamento
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {Object.entries(parseBusinessHours(company.businessHours) || {}).map(([day, hours]) => (
                          hours && (
                            <div key={day} className="flex justify-between items-center p-3 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                              <span className="text-sm font-medium text-neutral-700 dark:text-gray-300">
                                {day === 'monday' && 'Segunda-feira'}
                                {day === 'tuesday' && 'Terça-feira'}
                                {day === 'wednesday' && 'Quarta-feira'}
                                {day === 'thursday' && 'Quinta-feira'}
                                {day === 'friday' && 'Sexta-feira'}
                                {day === 'saturday' && 'Sábado'}
                                {day === 'sunday' && 'Domingo'}
                              </span>
                              <span className="text-sm text-neutral-600 dark:text-gray-400">
                                {typeof hours === 'string'
                                  ? hours
                                  : (hours.start && hours.end)
                                    ? `${hours.start} às ${hours.end}`
                                    : 'Fechado'}
                              </span>
                            </div>
                          )
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Aba: Assinatura (apenas System Admin) */}
              {activeTab === 'subscription' && isSystemAdmin && company.subscriptionInfo && (
                <div className="space-y-6">
                  {/* Status da Assinatura */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Package className="h-5 w-5 text-blue-500" />
                        Plano Atual
                      </h3>
                      <div className="space-y-3">
                        <div>
                          <p className="text-2xl font-bold text-neutral-900 dark:text-white">
                            {company.subscriptionInfo.planName}
                          </p>
                          <p className="text-sm text-neutral-500 dark:text-gray-400">
                            {company.subscriptionInfo.moduleCount} módulos ativos
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            company.subscriptionInfo.status === 'ACTIVE'
                              ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                              : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                          }`}>
                            {company.subscriptionInfo.status === 'ACTIVE' ? 'Ativo' : 'Inativo'}
                          </span>
                          <span className="text-xs text-neutral-500 dark:text-gray-400">
                            Desde {formatDate(company.subscriptionInfo.startDate)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-green-500" />
                        Faturamento
                      </h3>
                      <div className="space-y-3">
                        <div>
                          <p className="text-2xl font-bold text-neutral-900 dark:text-white">
                            {formatCurrency(company.subscriptionInfo.pricePerMonth)}
                          </p>
                          <p className="text-sm text-neutral-500 dark:text-gray-400">
                            {company.subscriptionInfo.billingCycle === 'YEARLY' ? 'Por ano' : 'Por mês'}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {company.subscriptionInfo.paymentStatus === 'up_to_date' && (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-sm text-green-600 dark:text-green-400">Pagamentos em dia</span>
                            </>
                          )}
                          {company.subscriptionInfo.paymentStatus === 'overdue' && (
                            <>
                              <XCircle className="h-4 w-4 text-red-500" />
                              <span className="text-sm text-red-600 dark:text-red-400">
                                Atrasado ({formatCurrency(company.subscriptionInfo.overdueAmount)})
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-purple-500" />
                        Próximo Vencimento
                      </h3>
                      <div className="space-y-3">
                        {company.subscriptionInfo.nextDueDate ? (
                          <>
                            <div>
                              <p className="text-2xl font-bold text-neutral-900 dark:text-white">
                                {formatDate(company.subscriptionInfo.nextDueDate)}
                              </p>
                              <p className="text-sm text-neutral-500 dark:text-gray-400">
                                {company.subscriptionInfo.daysUntilExpiry !== null && (
                                  company.subscriptionInfo.daysUntilExpiry <= 0
                                    ? 'Vencido'
                                    : `Em ${company.subscriptionInfo.daysUntilExpiry} dias`
                                )}
                              </p>
                            </div>
                            {company.subscriptionInfo.isNearExpiry && (
                              <div className="flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                <span className="text-sm text-yellow-600 dark:text-yellow-400">
                                  Próximo ao vencimento
                                </span>
                              </div>
                            )}
                          </>
                        ) : (
                          <p className="text-sm text-neutral-500 dark:text-gray-400">
                            Sem data de vencimento
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Módulos Contratados */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                      <Package className="h-5 w-5 text-blue-500" />
                      Módulos Contratados
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {company.subscriptionInfo.modules.map((module, index) => (
                        <div key={index} className="flex items-center gap-3 p-4 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                            <Package className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-neutral-900 dark:text-white">
                              {module.type || module}
                            </p>
                            <p className="text-xs text-neutral-500 dark:text-gray-400">
                              Ativo desde {formatDate(module.addedAt)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Uso de Recursos */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-green-500" />
                      Uso de Recursos
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-neutral-700 dark:text-gray-300">
                            Usuários
                          </span>
                          <span className="text-sm text-neutral-500 dark:text-gray-400">
                            {company.subscriptionInfo.userCount} / {company.subscriptionInfo.userLimit}
                          </span>
                        </div>
                        <div className="w-full bg-neutral-200 dark:bg-gray-700 rounded-full h-3">
                          <div
                            className={`h-3 rounded-full transition-all duration-300 ${
                              company.subscriptionInfo.userUsagePercentage >= 90
                                ? 'bg-red-500'
                                : company.subscriptionInfo.userUsagePercentage >= 70
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                            }`}
                            style={{ width: `${Math.min(company.subscriptionInfo.userUsagePercentage, 100)}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-neutral-500 dark:text-gray-400 mt-1">
                          {company.subscriptionInfo.userUsagePercentage}% utilizado
                          {company.subscriptionInfo.isNearUserLimit && (
                            <span className="text-yellow-600 dark:text-yellow-400 ml-2">
                              • Próximo ao limite
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Informações do Stripe */}
                  {(company.subscriptionInfo.stripeCustomerId || company.subscriptionInfo.stripeSubscriptionId) && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <CreditCard className="h-5 w-5 text-indigo-500" />
                        Informações de Pagamento
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {company.subscriptionInfo.stripeCustomerId && (
                          <div>
                            <p className="text-sm text-neutral-500 dark:text-gray-400">Customer ID</p>
                            <p className="text-sm font-mono text-neutral-900 dark:text-white">
                              {company.subscriptionInfo.stripeCustomerId}
                            </p>
                          </div>
                        )}
                        {company.subscriptionInfo.stripeSubscriptionId && (
                          <div>
                            <p className="text-sm text-neutral-500 dark:text-gray-400">Subscription ID</p>
                            <p className="text-sm font-mono text-neutral-900 dark:text-white">
                              {company.subscriptionInfo.stripeSubscriptionId}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Cupom */}
                      {company.subscriptionInfo.couponCode && (
                        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-center gap-2">
                            <Award className="h-4 w-4 text-green-600 dark:text-green-400" />
                            <span className="text-sm font-medium text-green-800 dark:text-green-300">
                              Cupom Aplicado: {company.subscriptionInfo.couponCode}
                            </span>
                          </div>
                          {company.subscriptionInfo.couponDiscount && (
                            <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                              Desconto: {company.subscriptionInfo.couponDiscount}%
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Histórico de Faturas */}
                  {company.subscriptionInfo.recentInvoices && company.subscriptionInfo.recentInvoices.length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                        <FileText className="h-5 w-5 text-purple-500" />
                        Histórico de Faturas
                      </h3>
                      <div className="space-y-3">
                        {company.subscriptionInfo.recentInvoices.slice(0, 5).map((invoice, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className={`w-2 h-2 rounded-full ${
                                invoice.status === 'PAID'
                                  ? 'bg-green-500'
                                  : invoice.status === 'PENDING'
                                    ? 'bg-yellow-500'
                                    : 'bg-red-500'
                              }`}></div>
                              <div>
                                <p className="text-sm font-medium text-neutral-900 dark:text-white">
                                  {formatCurrency(invoice.amount)}
                                </p>
                                <p className="text-xs text-neutral-500 dark:text-gray-400">
                                  Vencimento: {formatDate(invoice.dueDate)}
                                  {invoice.paidAt && ` • Pago em ${formatDate(invoice.paidAt)}`}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                invoice.status === 'PAID'
                                  ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                                  : invoice.status === 'PENDING'
                                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                                    : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                              }`}>
                                {invoice.status === 'PAID' ? 'Pago' : invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'}
                              </span>
                              {invoice.stripeInvoiceUrl && (
                                <a
                                  href={invoice.stripeInvoiceUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="p-1 text-neutral-400 dark:text-gray-500 hover:text-primary-500 dark:hover:text-primary-400"
                                  title="Ver fatura"
                                >
                                  <Download className="h-4 w-4" />
                                </a>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Aba: Usuários (apenas System Admin) */}
              {activeTab === 'users' && isSystemAdmin && company.users && (
                <div className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-neutral-900 dark:text-white flex items-center gap-2">
                        <Users className="h-5 w-5 text-blue-500" />
                        Usuários da Empresa ({company.users.length})
                      </h3>
                      <div className="text-sm text-neutral-500 dark:text-gray-400">
                        {company.subscriptionInfo && (
                          <span>
                            Limite: {company.subscriptionInfo.userLimit} usuários
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="space-y-3">
                      {company.users.map((user, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                              <Users className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-neutral-900 dark:text-white">
                                {user.fullName}
                              </p>
                              <p className="text-xs text-neutral-500 dark:text-gray-400">
                                {user.role === 'SYSTEM_ADMIN' ? 'Administrador do Sistema' :
                                 user.role === 'COMPANY_ADMIN' ? 'Administrador da Empresa' : 'Funcionário'}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-neutral-500 dark:text-gray-400">
                              Criado em {formatDate(user.createdAt)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {company.subscriptionInfo && company.subscriptionInfo.isNearUserLimit && (
                      <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                          <span className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                            Próximo ao limite de usuários
                          </span>
                        </div>
                        <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-1">
                          Esta empresa está usando {company.subscriptionInfo.userUsagePercentage}% do limite de usuários.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Aba: Atividade (apenas System Admin) */}
              {activeTab === 'activity' && isSystemAdmin && (
                <div className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                      <Activity className="h-5 w-5 text-green-500" />
                      Atividade da Empresa
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-3">
                          Informações Gerais
                        </h4>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-neutral-500 dark:text-gray-400">Data de Criação:</span>
                            <span className="text-sm font-medium text-neutral-900 dark:text-white">
                              {formatDateTime(company.createdAt)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-neutral-500 dark:text-gray-400">Última Atualização:</span>
                            <span className="text-sm font-medium text-neutral-900 dark:text-white">
                              {formatDateTime(company.updatedAt)}
                            </span>
                          </div>
                          {company.licenseValidUntil && (
                            <div className="flex justify-between">
                              <span className="text-sm text-neutral-500 dark:text-gray-400">Licença Válida Até:</span>
                              <span className="text-sm font-medium text-neutral-900 dark:text-white">
                                {formatDate(company.licenseValidUntil)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-3">
                          Status do Trial
                        </h4>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-neutral-500 dark:text-gray-400">É Trial:</span>
                            <span className={`text-sm font-medium ${
                              company.isTrial
                                ? 'text-yellow-600 dark:text-yellow-400'
                                : 'text-green-600 dark:text-green-400'
                            }`}>
                              {company.isTrial ? 'Sim' : 'Não'}
                            </span>
                          </div>
                          {company.trialStart && (
                            <div className="flex justify-between">
                              <span className="text-sm text-neutral-500 dark:text-gray-400">Início do Trial:</span>
                              <span className="text-sm font-medium text-neutral-900 dark:text-white">
                                {formatDate(company.trialStart)}
                              </span>
                            </div>
                          )}
                          {company.trialEnd && (
                            <div className="flex justify-between">
                              <span className="text-sm text-neutral-500 dark:text-gray-400">Fim do Trial:</span>
                              <span className="text-sm font-medium text-neutral-900 dark:text-white">
                                {formatDate(company.trialEnd)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Estatísticas de Uso */}
                    <div className="mt-6 pt-6 border-t border-neutral-200 dark:border-gray-700">
                      <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-4">
                        Estatísticas de Uso
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {company.users ? company.users.length : 0}
                          </div>
                          <div className="text-sm text-neutral-500 dark:text-gray-400">Usuários Ativos</div>
                        </div>
                        <div className="text-center p-4 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            {company.subscriptionInfo ? company.subscriptionInfo.moduleCount : 0}
                          </div>
                          <div className="text-sm text-neutral-500 dark:text-gray-400">Módulos Ativos</div>
                        </div>
                        <div className="text-center p-4 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {company.active ? 'Ativa' : 'Inativa'}
                          </div>
                          <div className="text-sm text-neutral-500 dark:text-gray-400">Status</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Aba: Configurações */}
              {activeTab === 'settings' && (
                <div className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-neutral-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-4 flex items-center gap-2">
                      <Settings className="h-5 w-5 text-gray-500" />
                      Configurações do Sistema
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-3">
                          Configurações Regionais
                        </h4>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-sm text-neutral-500 dark:text-gray-400">Moeda Padrão:</span>
                            <span className="text-sm font-medium text-neutral-900 dark:text-white">
                              {company.defaultCurrency || 'BRL'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-neutral-500 dark:text-gray-400">Fuso Horário:</span>
                            <span className="text-sm font-medium text-neutral-900 dark:text-white">
                              {company.timeZone || 'America/Sao_Paulo'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Configurações de Email */}
                      {company.emailConfigs && company.emailConfigs.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-3">
                            Configurações de Email
                          </h4>
                          <div className="space-y-3">
                            {company.emailConfigs.map((config, index) => (
                              <div key={index} className="p-3 bg-neutral-50 dark:bg-gray-700 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-neutral-900 dark:text-white">
                                      {config.emailFromName || 'Configuração de Email'}
                                    </p>
                                    <p className="text-xs text-neutral-500 dark:text-gray-400">
                                      {config.emailFromAddress}
                                    </p>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className={`w-2 h-2 rounded-full ${
                                      config.active ? 'bg-green-500' : 'bg-red-500'
                                    }`}></span>
                                    <span className="text-xs text-neutral-500 dark:text-gray-400">
                                      {config.active ? 'Ativo' : 'Inativo'}
                                    </span>
                                  </div>
                                </div>
                                <div className="mt-2 text-xs text-neutral-500 dark:text-gray-400">
                                  SMTP: {config.smtpHost}:{config.smtpPort}
                                  {config.smtpSecure && ' (SSL)'}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Informações Técnicas */}
                    <div className="mt-6 pt-6 border-t border-neutral-200 dark:border-gray-700">
                      <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-4">
                        Informações Técnicas
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <span className="text-sm text-neutral-500 dark:text-gray-400 block">ID da Empresa:</span>
                          <span className="text-sm font-mono text-neutral-900 dark:text-white">
                            {company.id}
                          </span>
                        </div>
                        {company.stripeCustomerId && (
                          <div>
                            <span className="text-sm text-neutral-500 dark:text-gray-400 block">Stripe Customer ID:</span>
                            <span className="text-sm font-mono text-neutral-900 dark:text-white">
                              {company.stripeCustomerId}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="text-center py-12 text-neutral-500 dark:text-neutral-400">
            <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">Nenhuma informação disponível</p>
            <p className="text-sm opacity-75">Não foi possível carregar os dados da empresa.</p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex justify-end gap-3 p-6 border-t border-neutral-200 dark:border-gray-700 bg-neutral-50 dark:bg-gray-800/50">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
        >
          Fechar
        </button>
      </div>
    </Modal>
  );
};

export default CompanyDetailsModal;
