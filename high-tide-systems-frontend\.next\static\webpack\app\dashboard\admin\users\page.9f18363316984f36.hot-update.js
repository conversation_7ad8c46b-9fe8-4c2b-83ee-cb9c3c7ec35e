"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/components/settings/CompanyManagementTab.js":
/*!*********************************************************!*\
  !*** ./src/components/settings/CompanyManagementTab.js ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyLogoService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyLogoService.js\");\n/* harmony import */ var _CompanyFormModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CompanyFormModal */ \"(app-pages-browser)/./src/components/settings/CompanyFormModal.js\");\n/* harmony import */ var _CompanyDetailsModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CompanyDetailsModal */ \"(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CompanyManagementTab = ()=>{\n    _s();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalCompanies, setTotalCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyModalOpen, setCompanyModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyDetailsModalOpen, setCompanyDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // Check user roles\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // If user is company admin or has a company ID but is not system admin, we should only show their company\n    const isCompanyAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'COMPANY_ADMIN' || (user === null || user === void 0 ? void 0 : user.companyId) && (user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompanyManagementTab.useEffect\": ()=>{\n            loadCompanies();\n        }\n    }[\"CompanyManagementTab.useEffect\"], []);\n    const loadCompanies = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage;\n        setIsLoading(true);\n        try {\n            // If company admin, just get their specific company\n            if (isCompanyAdmin && (user === null || user === void 0 ? void 0 : user.companyId)) {\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies/\".concat(user.companyId));\n                if (response.data) {\n                    setCompanies([\n                        response.data\n                    ]);\n                    setTotalCompanies(1);\n                    setTotalPages(1);\n                } else {\n                    setCompanies([]);\n                    setTotalCompanies(0);\n                    setTotalPages(1);\n                }\n            } else {\n                // System admin sees all companies\n                const params = {\n                    page,\n                    limit: 10,\n                    search: search || undefined,\n                    active: statusFilter || undefined\n                };\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies\", {\n                    params\n                });\n                setCompanies(response.data.companies || []);\n                setTotalCompanies(response.data.total || 0);\n                setTotalPages(response.data.pages || 1);\n            }\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Error loading companies:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        loadCompanies(1);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        loadCompanies(1);\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        loadCompanies(page);\n    };\n    const handleAddCompany = ()=>{\n        setSelectedCompany(null);\n        setCompanyModalOpen(true);\n    };\n    const handleEditCompany = (company)=>{\n        setSelectedCompany(company);\n        setCompanyModalOpen(true);\n    };\n    const handleViewCompany = (companyId)=>{\n        setSelectedCompanyId(companyId);\n        setCompanyDetailsModalOpen(true);\n    };\n    const handleToggleCompanyStatus = (company)=>{\n        if (!isSystemAdmin) return; // Only system admins can change status\n        setSelectedCompany(company);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(company.active ? \"Desativar\" : \"Ativar\", \" a empresa \").concat(company.name, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteCompany = (company)=>{\n        if (!isSystemAdmin) return; // Only system admins can delete\n        setSelectedCompany(company);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente a empresa \".concat(company.name, \"?\"),\n            variant: \"danger\"\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const confirmAction = async ()=>{\n        if (!selectedCompany || !isSystemAdmin) return;\n        try {\n            if (actionToConfirm.type === \"toggle-status\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.patch(\"/companies/\".concat(selectedCompany.id, \"/status\"));\n            } else if (actionToConfirm.type === \"delete\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.delete(\"/companies/\".concat(selectedCompany.id));\n            }\n            loadCompanies();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error performing action:\", error);\n            alert(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Ocorreu um erro ao executar esta ação\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-100 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            isSystemAdmin ? \"Gerenciamento de Empresas\" : \"Minha Empresa\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleAddCompany,\n                        className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Nova Empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome, nome fantasia ou CNPJ...\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-500 dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"true\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"false\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"sm:hidden h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    className: \"px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"sm:hidden h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, undefined) : companies.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-500 dark:text-gray-400\",\n                        children: \"Nenhuma empresa encontrada\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 240,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, undefined) : isCompanyAdmin ? // Company Admin sees just their company as a card\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-neutral-800 dark:text-white\",\n                                                        children: companies[0].name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                                        children: companies[0].tradingName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-0.5 rounded-full \".concat(companies[0].active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                                            children: companies[0].active ? \"Ativa\" : \"Inativa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewCompany(companies[0].id),\n                                                className: \"px-3 py-1.5 bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-md hover:bg-neutral-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Detalhes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditCompany(companies[0]),\n                                                className: \"px-3 py-1.5 bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-md hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Editar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"CNPJ:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].cnpj ? companies[0].cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Telefone:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].phone || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].phone2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Telefone 2:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].phone2\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Endere\\xe7o\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Endere\\xe7o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].address || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Cidade/Estado:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].city && companies[0].state ? \"\".concat(companies[0].city, \", \").concat(companies[0].state) : \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"CEP:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].postalCode || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Online\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Website:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].website || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Email:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].contactEmail || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Outras Informa\\xe7\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Moeda:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].defaultCurrency || \"BRL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Fuso Hor\\xe1rio:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].timeZone || \"America/Sao_Paulo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400 block mb-1\",\n                                                                children: \"Descri\\xe7\\xe3o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200\",\n                                                                children: companies[0].description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, undefined) : // System Admin sees a table of all companies\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Empresas\",\n                    columns: [\n                        {\n                            header: 'Empresa',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'CNPJ',\n                            field: 'cnpj',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Plano',\n                            field: 'plan',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Pagamento',\n                            field: 'payment',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'status',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: companies,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma empresa encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 393,\n                        columnNumber: 24\n                    }, void 0),\n                    currentPage: currentPage,\n                    totalPages: totalPages,\n                    totalItems: totalCompanies,\n                    onPageChange: handlePageChange,\n                    showPagination: totalPages > 1,\n                    tableId: \"admin-companies-table\",\n                    enableColumnToggle: true,\n                    renderRow: (company, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 flex items-center justify-center\",\n                                                children: company.documents && company.documents[0] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-10 w-10 rounded-full object-cover border border-neutral-200 dark:border-gray-600\",\n                                                        src: company.documents && company.documents[0] && company.documents[0].path ? _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__.companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : '',\n                                                        alt: company.name,\n                                                        onLoad: ()=>console.log(\"[CompanyManagementTab] Imagem da empresa \".concat(company.id, \" carregada com sucesso\")),\n                                                        onError: (e)=>{\n                                                            console.error(\"[CompanyManagementTab] Erro ao carregar imagem da empresa \".concat(company.id, \":\"), e.target.src);\n                                                            e.target.onerror = null;\n                                                            e.target.style.display = 'none';\n                                                            e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"20\" height=\"14\" x=\"2\" y=\"7\" rx=\"2\" ry=\"2\"/><path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"/></svg></div>';\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 406,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100\",\n                                                        children: company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    company.tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-neutral-500 dark:text-neutral-400\",\n                                                        children: company.tradingName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 429,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 405,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 404,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('cnpj') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                    children: company.cnpj ? company.cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 443,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('contact') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: [\n                                        company.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-neutral-600 dark:text-neutral-300 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                company.phone\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 452,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        company.contactEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-neutral-600 dark:text-neutral-300 flex items-center mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                company.contactEmail\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 458,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(company.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                        children: company.active ? \"Ativa\" : \"Inativa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 467,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 466,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4 text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewCompany(company.id),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                title: \"Visualizar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 481,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditCompany(company),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleCompanyStatus(company),\n                                                className: \"p-1 transition-colors \".concat(company.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                title: company.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 497,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteCompany(company),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 509,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 480,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 479,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 402,\n                            columnNumber: 15\n                        }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            companyModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompanyFormModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: companyModalOpen,\n                onClose: ()=>setCompanyModalOpen(false),\n                company: selectedCompany,\n                onSuccess: ()=>{\n                    setCompanyModalOpen(false);\n                    loadCompanies();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 527,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompanyDetailsModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: companyDetailsModalOpen,\n                onClose: ()=>setCompanyDetailsModalOpen(false),\n                companyId: selectedCompanyId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.variant) || \"warning\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompanyManagementTab, \"9dHN0xgRBQS8/O5mdeEH5tqyYdQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = CompanyManagementTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyManagementTab);\nvar _c;\n$RefreshReg$(_c, \"CompanyManagementTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/CompanyManagementTab.js\n"));

/***/ })

});